import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register', '/auth-redirect', '/bind', '/register']

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        console.log('用户角色为空，开始获取用户信息...')
        // 判断当前用户是否已拉取完user_info信息
        try {
          console.log('调用 user/getInfo...')
          await store.dispatch('user/getInfo')
          console.log('用户信息获取成功，当前角色:', store.getters.roles)

          console.log('调用 permission/generateRoutes...')
          const accessRoutes = await store.dispatch('permission/generateRoutes')
          console.log('生成的访问路由:', accessRoutes)

          // 根据roles权限生成可访问的路由表
          router.addRoutes(accessRoutes) // 动态添加可访问路由表
          console.log('路由添加完成，重定向到目标页面')
          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        } catch (error) {
          console.error('获取用户信息或生成路由失败:', error)
          // 移除token并跳转登录页
          await store.dispatch('user/fedLogOut')
          Message.error(error || 'Verification failed, please Login again.')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      } else {
        console.log('用户已有角色信息，直接通过:', store.getters.roles)
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
